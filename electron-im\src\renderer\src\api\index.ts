// API 请求服务
import { API_CONFIG, APP_CONFIG } from '../config'

// 用户信息接口
export interface User {
  id: string
  username: string
  email: string
  displayName: string
  avatar: string
  department: string
  position: string
  isOnline: boolean
  lastOnlineTime: string
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  token: string
  user: User
}

// API 错误接口
export interface ApiError {
  success: false
  message: string
  code?: string
}

// HTTP 客户端类
class ApiClient {
  private baseURL: string
  private timeout: number

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
  }

  // 通用请求方法
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY)

    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers
      }
    }

    // 设置超时
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)
    config.signal = controller.signal

    try {
      const response = await fetch(url, config)
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      clearTimeout(timeoutId)
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时')
        }
        throw error
      }
      throw new Error('网络请求失败')
    }
  }

  // 登录方法
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await this.request<LoginResponse>(API_CONFIG.ENDPOINTS.LOGIN, {
        method: 'POST',
        body: JSON.stringify(credentials)
      })

      if (response.success && response.token) {
        // 保存 token 和用户信息
        localStorage.setItem(APP_CONFIG.TOKEN_KEY, response.token)
        localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(response.user))
      }

      return response
    } catch (error) {
      console.error('登录请求失败:', error)
      throw error
    }
  }

  // 登出方法
  logout(): void {
    localStorage.removeItem(APP_CONFIG.TOKEN_KEY)
    localStorage.removeItem(APP_CONFIG.USER_KEY)
  }

  // 获取当前用户信息
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem(APP_CONFIG.USER_KEY)
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch {
        return null
      }
    }
    return null
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    return !!localStorage.getItem(APP_CONFIG.TOKEN_KEY)
  }

  // 获取 token
  getToken(): string | null {
    return localStorage.getItem(APP_CONFIG.TOKEN_KEY)
  }
}

// 导出 API 客户端实例
export const apiClient = new ApiClient()
