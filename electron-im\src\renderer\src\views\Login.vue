<!-- 登录页 -->
<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md">
      <div>
        <h2 class="mt-6 text-center text-3xl font-bold text-gray-900">用户登录</h2>
      </div>

      <form @submit.prevent="handleLogin" class="mt-8 space-y-6">
        <div class="space-y-4">
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700"> 用户名 </label>
            <input
              id="username"
              v-model="username"
              type="text"
              :class="[
                'mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                usernameError ? 'border-red-300' : 'border-gray-300'
              ]"
              placeholder="请输入用户名"
            />
            <p v-if="usernameError" class="mt-1 text-sm text-red-600">
              {{ usernameError }}
            </p>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700"> 密码 </label>
            <input
              id="password"
              v-model="password"
              type="password"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入密码"
            />
          </div>
        </div>

        <div v-if="errorMessage" class="bg-red-50 border border-red-200 rounded-md p-3">
          <p class="text-sm text-red-600">{{ errorMessage }}</p>
        </div>

        <div v-if="successMessage" class="bg-green-50 border border-green-200 rounded-md p-3">
          <p class="text-sm text-green-600">{{ successMessage }}</p>
        </div>

        <button
          type="submit"
          :disabled="isLoading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isLoading" class="mr-2">
            <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </span>
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const username = ref('')
const password = ref('')
const usernameError = ref('')
const errorMessage = ref('')
const successMessage = ref('')
const isLoading = ref(false)

// WebSocket服务类
class WebSocketService {
  private ws: WebSocket | null = null
  private url: string

  constructor(url: string = 'ws://localhost:8080') {
    this.url = url
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url)

        this.ws.onopen = () => {
          console.log('[v0] WebSocket连接已建立')
          resolve()
        }

        this.ws.onerror = (error) => {
          console.error('[v0] WebSocket连接错误:', error)
          reject(new Error('WebSocket连接失败'))
        }

        this.ws.onclose = () => {
          console.log('[v0] WebSocket连接已关闭')
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  async login(username: string, password: string): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        reject(new Error('WebSocket未连接'))
        return
      }

      const loginData = {
        type: 'login',
        username,
        password,
        timestamp: Date.now()
      }

      // 设置消息监听器
      const messageHandler = (event: MessageEvent) => {
        try {
          const response = JSON.parse(event.data)
          console.log('[v0] 收到服务器响应:', response)

          if (response.type === 'login_response') {
            this.ws?.removeEventListener('message', messageHandler)

            if (response.success) {
              resolve({ success: true, message: response.message || '登录成功' })
            } else {
              resolve({ success: false, message: response.message || '登录失败' })
            }
          }
        } catch (error) {
          console.error('[v0] 解析服务器响应失败:', error)
          this.ws?.removeEventListener('message', messageHandler)
          reject(new Error('服务器响应格式错误'))
        }
      }

      this.ws.addEventListener('message', messageHandler)

      // 发送登录请求
      this.ws.send(JSON.stringify(loginData))
      console.log('[v0] 已发送登录请求:', loginData)

      // 设置超时
      setTimeout(() => {
        this.ws?.removeEventListener('message', messageHandler)
        reject(new Error('登录请求超时'))
      }, 10000)
    })
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}

// 创建WebSocket服务实例
const wsService = new WebSocketService()

// 表单校验
const validateForm = (): boolean => {
  usernameError.value = ''

  if (!username.value.trim()) {
    usernameError.value = '用户名不能为空'
    return false
  }

  if (username.value.trim().length < 2) {
    usernameError.value = '用户名至少需要2个字符'
    return false
  }

  return true
}

// 处理登录
const handleLogin = async () => {
  // 清除之前的消息
  errorMessage.value = ''
  successMessage.value = ''

  // 表单校验
  if (!validateForm()) {
    return
  }

  isLoading.value = true

  try {
    // 模拟WebSocket连接和登录过程
    console.log('[v0] 开始登录流程')

    // 模拟不同的登录结果
    const mockResponse = await simulateLogin(username.value, password.value)

    if (mockResponse.success) {
      successMessage.value = mockResponse.message
      console.log('[v0] 登录成功')

      // 这里可以添加登录成功后的逻辑，比如跳转到主页
      setTimeout(() => {
        console.log('[v0] 准备跳转到主页...')
      }, 1500)
    } else {
      errorMessage.value = mockResponse.message
      console.log('[v0] 登录失败:', mockResponse.message)
    }
  } catch (error) {
    console.error('[v0] 登录过程中发生错误:', error)
    errorMessage.value = error instanceof Error ? error.message : '网络连接失败，请检查网络设置'
  } finally {
    isLoading.value = false
  }
}

// 模拟登录响应（用于演示）
const simulateLogin = async (
  username: string,
  password: string
): Promise<{ success: boolean; message: string }> => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 1500))

  // 模拟不同的登录场景
  if (username === 'admin' && password === 'admin') {
    return { success: true, message: '登录成功！欢迎回来' }
  } else if (username === 'existing_user') {
    return { success: false, message: '用户名已存在，请选择其他用户名' }
  } else if (username === 'blocked_user') {
    return { success: false, message: '账户已被锁定，请联系管理员' }
  } else if (password === 'wrong') {
    return { success: false, message: '密码错误，请重新输入' }
  } else if (username === 'server_error') {
    throw new Error('服务器内部错误，请稍后重试')
  } else {
    return { success: false, message: '用户名或密码错误' }
  }
}

// 组件卸载时断开WebSocket连接
import { onUnmounted } from 'vue'
onUnmounted(() => {
  wsService.disconnect()
})
</script>
