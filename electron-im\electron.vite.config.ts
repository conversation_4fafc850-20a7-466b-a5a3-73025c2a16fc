import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()]
  },
  preload: {
    plugins: [externalizeDepsPlugin()]
  },
  renderer: {
    base: '/zhuyuqian',
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src')
        // '@': resolve('src/renderer/src')
      }
    },
    plugins: [vue(), tailwindcss()],
    server: {
      host: '0.0.0.0',
      port: 3000,
      proxy: {
        '/api': {
          target: 'https://simple.im.wps.cn',
          changeOrigin: true
        }
      }
    },
    css: {
      postcss: {
        plugins: [tailwindcss]
      }
    },
    build: {
      rollupOptions: {
        input: {
          main: resolve('src/renderer/index.html')
        }
      }
    }
  }
})
